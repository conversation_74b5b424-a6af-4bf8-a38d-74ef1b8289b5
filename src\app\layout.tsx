import Providers from "@/providers";
import type { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { dmSans } from "./fonts";
import "./globals.css";

export const metadata: Metadata = {
  title: "Country Life",
  description: "",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${dmSans.className}`}>
        <Providers>
          <Suspense>{children}</Suspense>
        </Providers>
      </body>
    </html>
  );
}
