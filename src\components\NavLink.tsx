"use client";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ComponentProps } from "react";

const NavLink = (props: ComponentProps<typeof Link>) => {
  const pathname = usePathname();
  const regex = new RegExp(`^${props.href}`);
  const className = props.className;
  return (
    <Link
      {...props}
      href={props.href}
      className={cn(
        className,
        "hover:bg-secondary-yellow hover:text-primary-orange flex items-center gap-4 rounded-[10px] px-2 py-2 border border-transparent hover:border",
        regex.test(pathname) &&
          "border-primary-orange text-primary-orange bg-secondary-yellow border"
      )}
    />
  );
};

export default NavLink;
