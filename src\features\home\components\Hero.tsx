import fruitsCompositionImage from "@/assets/images/fruits-composition-image.png";
import Image from "next/image";
import Link from "next/link";

const Hero = () => {
  return (
    <section className="relative mx-auto h-screen overflow-x-hidden bg-[#F4F3EA] pt-[8rem] pl-[10rem]">
      <div className="space-y-6 lg:w-[40%]">
        <p className="text-[2.5rem] leading-[1.2] font-medium text-[#2A3A34] lg:text-[3.5rem]">Nourish Your Life with Fresh, Healthy Choices</p>
        <p className="text-lg leading-[1.5]">
          Enjoy farm-fresh meals curated for your lifestyle. Eat better, live healthier, and support sustainability.
        </p>
        <Link className="grid h-[3rem] w-[8.5rem] place-items-center rounded-[30px] bg-[#2C463D] font-medium text-white" href="">
          Shop
        </Link>
      </div>
      <Image
        src={fruitsCompositionImage}
        alt="Fruits Composition"
        className="absolute -right-10 -bottom-10 size-[12rem] object-contain lg:-right-12 lg:-bottom-0 lg:size-[28rem]"
      />
    </section>
  );
};

export default Hero;
