import Image from "next/image";
import Link from "next/link";
import organicProductsImage from "@/assets/images/organic-products.png";
import farmBoxImage from "@/assets/images/farm-box.png";
import personalizedBundlesImage from "@/assets/images/personalized-bundles.png";

const ExploreProducts = () => {
  return (
    <section>
      <div className="max-w-[90rem] px-[2rem] pt-[2rem] lg:px-[4rem]">
        <p className="text-center text-lg font-bold text-[#1A1A1A] md:text-2xl lg:text-[2.8rem]">
          Explore products suited to your lifestyle and health.
        </p>
        <div className="mt-8 grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="row-span-2 rounded-[16px] bg-[#FFF6EA] px-4 py-4">
            <div className="flex items-center gap-2">
              <div>
                <p className="md:text-lg lg:text-4xl">Fresh, Organic Products for Everyday Wellness</p>
                <p className="mt-2">Make mindful choices that nourish your body </p>
                <Link
                  className="my-6 grid h-[2.8rem] w-[7.8rem] place-items-center rounded-[30px] bg-[#4D8526] font-medium text-white"
                  href="https://countrylife676.bumpa.shop/"
                >
                  Shop Now
                </Link>
                {/* <Image src={organicProductsImage} alt="Organic Products" className="w-full h-full" /> */}
              </div>
            </div>
          </div>
          <div className="rounded-[16px] bg-[#E2E7CD] px-4 py-4">
            <div>
              <p className="md:text-lg lg:text-4xl">Country Life Farm Box</p>
              <p className="mt-2">Discover products tailored to your wellness journey. </p>
              <Link
                className="my-6 grid h-[2.8rem] w-[7.8rem] place-items-center rounded-[30px] bg-[#BD6703] font-medium text-white"
                href="https://countrylife676.bumpa.shop/"
              >
                Shop Now
              </Link>
            </div>
          </div>
          <div className="rounded-[16px] bg-[#FFE9E9] px-4 py-4 pb-[10rem] relative overflow-hidden">
            <div>
              <p className="md:text-lg lg:text-4xl">Personalized Bundles for You and Your Family</p>
              <Link
                className="my-6 grid h-[2.8rem] w-[7.8rem] place-items-center rounded-[30px] bg-[#4D8526] font-medium text-white"
                href="https://countrylife676.bumpa.shop/"
              >
                Shop Now
              </Link>
            </div>
              <Image src={personalizedBundlesImage} alt="Personalized Bundles" className="h-[20rem] w-[25rem] object-cover absolute bottom-0 -right-10" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExploreProducts;
