import farmBoxImage from "@/assets/images/farm-box.png";
import organicProductsImage from "@/assets/images/organic-products.png";
import personalizedBundlesImage from "@/assets/images/personalized-bundles.png";
import Image from "next/image";
import Link from "next/link";

const ExploreProducts = () => {
  return (
    <section>
      <div className="mx-auto max-w-[90rem] px-2 pt-[2rem] md:px-[2rem] lg:px-[4rem]">
        <p className="text-center text-lg font-bold text-[#1A1A1A] md:text-2xl lg:text-[2.8rem]">
          Explore products suited to your lifestyle and health.
        </p>
        <div className="mt-8 grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="relative row-span-2 overflow-hidden rounded-[16px] bg-[#FFE9E9] px-4 pt-4 pb-4 md:px-8 md:pt-8 md:pb-[5rem] lg:px-14 lg:pt-[5rem]">
            <div className="w-[60%] xl:w-[80%]">
              <p className="font-bold text-[#071C1F] md:text-lg lg:text-4xl">Fresh, Organic Products for Everyday Wellness</p>
              <p className="mt-2 text-sm text-[#354E43] md:text-base">Make mindful choices that nourish your body </p>
              <Link
                className="my-6 grid h-[2.8rem] w-[7.8rem] place-items-center rounded-[30px] bg-[#4D8526] font-medium text-white"
                href="https://countrylife676.bumpa.shop/"
                target="_blank"
              >
                Shop Now
              </Link>
              <Image
                src={organicProductsImage}
                alt="Organic Products"
                className="absolute -right-10 -bottom-12 size-[12rem] -rotate-[25deg] object-contain lg:-right-24 lg:-bottom-20 lg:size-[24rem]"
              />
            </div>
          </div>
          <div className="relative overflow-hidden rounded-[16px] bg-[#FFE9E9] px-4 pt-4 pb-4 md:px-6 md:pb-[5rem]">
            <div className="w-[60%] xl:w-[80%]">
              <p className="font-bold text-[#071C1F] md:text-lg lg:text-4xl">Country Life Farm Box</p>
              <p className="mt-2 text-sm text-[#354E43] md:text-base">Discover products tailored to your wellness journey</p>
              <Link
                className="my-6 grid h-[2.8rem] w-[7.8rem] place-items-center rounded-[30px] bg-[#BD6703] font-medium text-white"
                href="https://countrylife676.bumpa.shop/"
                target="_blank"
              >
                Shop Now
              </Link>
            </div>
            <Image
              src={farmBoxImage}
              alt="Farm Box"
              className="absolute -right-10 -bottom-10 size-[12rem] object-contain lg:-right-18 lg:-bottom-16 lg:size-[18rem]"
            />
          </div>
          <div className="relative overflow-hidden rounded-[16px] bg-[#FFE9E9] px-4 pt-4 pb-4 md:px-6 md:pb-[5rem]">
            <div className="w-[60%] xl:w-[80%]">
              <p className="font-bold text-[#071C1F] md:text-lg lg:text-xl xl:text-4xl">Personalized Bundles for You and Your Family</p>
              <p className="mt-2 text-sm text-[#354E43] md:text-base">Make mindful choices that nourish your body</p>
              <Link
                className="my-6 grid h-[2.8rem] w-[7.8rem] place-items-center rounded-[30px] bg-[#4D8526] font-medium text-white"
                href="https://countrylife676.bumpa.shop/"
                target="_blank"
              >
                Shop Now
              </Link>
            </div>
            <Image
              src={personalizedBundlesImage}
              alt="Personalized Bundles"
              className="absolute -right-10 -bottom-10 size-[12rem] object-contain lg:-right-12 lg:-bottom-12 lg:size-[18rem]"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExploreProducts;
