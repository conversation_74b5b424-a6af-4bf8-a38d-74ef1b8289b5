"use client";
import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import CountryLifeLogo from "@/assets/images/country-life-logo.svg";
import cartIcon from "@/assets/icons/cart.svg";
import userIcon from "@/assets/icons/account.svg";
import searchIcon from "@/assets/icons/search.svg";
import breadCrumb from "@/assets/icons/bread-crumb.svg";
import { dmSans } from '@/app/fonts';


export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const toggleMenu = () => setIsMenuOpen((prev) => !prev);

  return (
    <header className="sticky w-full md:h-[88px] h-[64px] flex justify-between items-center  px-6 py-4 bg-[#F4F3EA] bg-opacity-60 text-white">
      {/* INFO SECTION */}
      <div className="flex gap-6 items-center">
        <button onClick={toggleMenu} className="md:hidden">
          <Image src={breadCrumb} alt="Bread Crumb" className="w-6 h-6" />
        </button>
        <Image
          src={CountryLifeLogo}
          alt="country Life Logo"
          className="w-[100px] h-[50px]"
        />
      </div>

      {/* Navigation Pane */}
        <div className={`justify-between gap-8 hidden text-2xl md:flex ${dmSans.className}`}>    
        <Link
          href="/" // Ensure this matches your file system route (e.g., src/app/signup/page.tsx)
          className="text-[#2D3131] font-medium"
        >
          Fresh Foods
        </Link>
        <Link
          href="/" // Ensure this matches your file system route (e.g., src/app/signup/page.tsx)
          className="text-[#2D3131] font-medium"
        >
          Meal Plans
        </Link>
        <Link
          href="/" // Ensure this matches your file system route (e.g., src/app/signup/page.tsx)
          className="text-[#2D3131] font-medium"
        >
          Health Tips
        </Link>
        <Link
          href="/" // Ensure this matches your file system route (e.g., src/app/signup/page.tsx)
          className="text-[#2D3131] font-medium"
        >
          Wellness Hub
        </Link>
      </div>

      {/* User Interaction */}
      <div className="flex gap-6">
        <div>
          <Image src={searchIcon} alt="search icon" />
        </div>
        <div>
          <Image src={userIcon} alt="user icon" />
        </div>
        <div>
          <Image src={cartIcon} alt="cart icon" />
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        // This should toggle sidebar actually
        <div className="mt-4 flex flex-col gap-4 text-[#000] md:hidden">
          <Link href="/" onClick={toggleMenu}>
            Fresh Foods
          </Link>
          <Link href="/" onClick={toggleMenu}>
            Meal Plans
          </Link>
          <Link href="/" onClick={toggleMenu}>
            Health Tips
          </Link>
          <Link href="/" onClick={toggleMenu}>
            Wellness Hub
          </Link>
        </div>
      )}
    </header>
  );
}
